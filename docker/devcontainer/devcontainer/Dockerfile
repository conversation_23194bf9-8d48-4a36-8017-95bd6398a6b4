# Start with Ubuntu 24.04 and Python 3.12.1
FROM python:3.12.1-slim

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    unzip \
    jq \
    git \
    && rm -rf /var/lib/apt/lists/* 

# Install specific Node.js version (example: Node.js 22.14.0)
RUN apt-get update && apt-get install -y ca-certificates gnupg \
    && mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_22.x nodistro main" > /etc/apt/sources.list.d/nodesource.list \
    && apt-get update && apt-get install -y nodejs=22.14.0* \
    && rm -rf /var/lib/apt/lists/* \
    && rm /bin/python3 \
    && rm -rf /bin/python3.11

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64-2.24.10.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf aws awscliv2.zip

# Install Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/download/v2.37.1/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose \
    && chmod +x /usr/local/bin/docker-compose

# Set working directory
WORKDIR /workspace

RUN npm init -y

# Clear cache to avoid conflicts
RUN npm cache clean --force

# Ensure package-lock.json is created
RUN npm install --save-exact aws-cdk@2.1000.2 --package-lock-only
RUN npm install

# Add node_modules/.bin to PATH
ENV PATH="/workspace/node_modules/.bin:$PATH"

# Acknowledge the versioning warning
RUN npx cdk acknowledge 32775
RUN npx cdk acknowledge 34892

# Create a non-root user to match VS Code's default
RUN useradd -m -s /bin/bash vscode

# Install sudo and configure permissions
RUN apt-get update && apt-get install -y sudo && rm -rf /var/lib/apt/lists/*
RUN echo "vscode ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

USER vscode
