# Start with Ubuntu 24.04 and Python 3.12.1
FROM python:3.12.1-slim

# Set working directory
WORKDIR /app

# Install system dependencies in a single layer
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements.txt first for better layer caching
# This layer will only rebuild if requirements change
COPY docker/extraction/extraction/requirements.txt .

# Install Python dependencies
# This layer will only rebuild if requirements.txt changes
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared code first (changes less frequently)
COPY shared/ /app/shared/

# Copy application code last (changes most frequently)
COPY src/tasks/extraction/ /app/src/tasks/extraction/

# Set environment variables
ENV AWS_CONFIG_PATH=/app/shared/config/aws_config.yaml
ENV AWS_REGION=us-east-2
ENV ENDPOINTS=teams
ENV PYTHONPATH=/app

# Set the entry point to extraction.py
CMD ["python", "src/tasks/extraction/extraction.py"]
# CMD ["tail", "-f", "/dev/null"]

