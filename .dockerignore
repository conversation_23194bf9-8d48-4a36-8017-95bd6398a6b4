# Git and version control
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
*.tar
*.tar.gz
*.zip

# AWS and deployment
.aws/
cdk.out/
.deployment-marker
.build-marker

# GitHub Actions
.github/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Exclude other docker directories when building specific services
docker/*/
!docker/extraction/extraction/requirements.txt
!docker/prefect/*/requirements.txt
!docker/devcontainer/*/requirements.txt
