import asyncio
import importlib
import json
import os

import boto3
from botocore.exceptions import Client<PERSON>rror
from prefect import flow, get_client
from prefect.client.schemas.actions import WorkPoolCreate, WorkPoolUpdate
from prefect.client.schemas.filters import DeploymentFilter
from prefect.exceptions import ObjectAlreadyExists
from prefect.runner.storage import GitRepository
from prefect_github import GitHubCredentials

from shared.python.utils.ecs_utils import get_image_uri_from_task_arn
from shared.python.utils.get_aws_config import get_aws_config, get_config
from shared.python.utils.process_resources import (
    get_param_values_from_stack_config,
    get_process_resource_ids,
    get_subnets_by_name_keyword,
)
from shared.python.utils.secret_helpers import get_secret_value, get_secrets_from_config


def transform_resource_ids_with_arns(resource_ids_dict, aws_config):
    """
    Transform resource IDs dictionary to include both ID and ARN for each resource.

    Args:
        resource_ids_dict (dict): Dictionary with resource types as keys and lists of IDs as values
        aws_config (dict): AWS configuration dictionary

    Returns:
        dict: Transformed dictionary with each resource having both 'id' and 'arn' fields
    """
    transformed_resources = {}
    stacks_config = aws_config.get("infrastructure", {}).get("stacks", aws_config)

    for resource_type, resource_ids in resource_ids_dict.items():
        transformed_resources[resource_type] = []

        for resource_id in resource_ids:
            resource_arn = get_param_values_from_stack_config(
                stacks_config, resource_id
            )
            transformed_resources[resource_type].append(
                {"id": resource_id, "arn": resource_arn}
            )

    return transformed_resources


def extract_arns_from_resource(resource_list):
    """
    Extract all ARNs from a specific transformed resource into a single list.

    Args:
        resource_list (list): List of resource dictionaries, each containing 'id' and 'arn' keys

    Returns:
        list: List of ARN strings extracted from the resource

    Example:
        Input: [{'id': 'cluster-1', 'arn': 'arn:aws:ecs:...'}, {'id': 'cluster-2', 'arn': 'arn:aws:ecs:...'}]
        Output: ['arn:aws:ecs:...', 'arn:aws:ecs:...']
    """
    return [
        resource.get("arn")
        for resource in resource_list
        if resource.get("arn") is not None
    ]


def retrieve_job_template(
    template_path: str = "deployment/aws_deployment/scripts/start_orchestrator/prefect_operational_bootstrap/pool_job_template.json",
) -> dict:
    """
    Retrieve the job template from the pool_job_template.json file.

    Args:
        template_path (str): Path to the job template JSON file

    Returns:
        dict: The job template configuration loaded from the JSON file
    """
    try:
        with open(template_path, "r") as file:
            job_template = json.load(file)
        return job_template
    except FileNotFoundError:
        raise FileNotFoundError(f"Job template file not found at {template_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in job template file: {e}")
    except Exception as e:
        raise RuntimeError(f"Error reading job template file: {e}")


def _substitute_string_placeholders(value: str, substitutions: dict):
    """
    Substitute placeholders in a string with values from substitutions dictionary.

    Args:
        value (str): String that may contain placeholders like "{key_name}"
        substitutions (dict): Dictionary of key-value pairs for substitution

    Returns:
        str or list: String with placeholders replaced, or the actual substitution value
                    if the entire value is a placeholder and the substitution is a list
    """
    result = value
    for sub_key, sub_value in substitutions.items():
        placeholder = "{" + sub_key + "}"
        if result == placeholder:
            # If the entire value is just a placeholder, return the actual substitution value
            # This preserves the original data type (list, dict, etc.)
            return sub_value
        elif placeholder in result:
            result = result.replace(placeholder, str(sub_value))
    return result


def substitute_job_template_defaults(job_template: dict, substitutions: dict) -> dict:
    """
    Substitute default values in the job template with provided values.

    This function recursively traverses the job template dictionary and replaces
    default values that match keys in the substitutions dictionary. It specifically
    looks for "default" keys and substitutes placeholder values like "{key_name}"
    with the corresponding values from the substitutions dictionary. It handles both
    string defaults and dictionary defaults with nested placeholders.

    Args:
        job_template (dict): The job template dictionary to modify
        substitutions (dict): Dictionary of key-value pairs for substitution

    Returns:
        dict: A new job template dictionary with substituted default values

    Example:
        template = {"variables": {"properties": {"cluster": {"default": "{cluster}"}}}}
        substitutions = {"cluster": "my-cluster"}
        result = substitute_job_template_defaults(template, substitutions)
        # result["variables"]["properties"]["cluster"]["default"] == "my-cluster"
    """
    import copy

    # Create a deep copy to avoid modifying the original template
    modified_template = copy.deepcopy(job_template)

    def _substitute_recursive(obj):
        """Recursively substitute values in nested dictionaries and lists."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == "default":
                    if isinstance(value, str):
                        # Handle string template substitution
                        obj[key] = _substitute_string_placeholders(value, substitutions)
                    elif isinstance(value, dict):
                        # Handle dictionary defaults - substitute placeholders in each value
                        for nested_key, nested_value in value.items():
                            if isinstance(nested_value, str):
                                value[nested_key] = _substitute_string_placeholders(
                                    nested_value, substitutions
                                )
                            else:
                                _substitute_recursive(nested_value)
                    else:
                        # Handle other types (lists, etc.) recursively
                        _substitute_recursive(value)
                else:
                    # Recursively process nested structures
                    _substitute_recursive(value)
        elif isinstance(obj, list):
            for item in obj:
                _substitute_recursive(item)

    _substitute_recursive(modified_template)
    return modified_template


async def ensure_work_pool(
    client, work_pool_name: str, pool_type: str, job_template: dict = None
):
    """
    Ensure the work pool exists, creating it if necessary or updating it if it exists.

    Args:
        client: Prefect client instance
        work_pool_name: Name of the work pool
        pool_type: Type of the work pool ("ecs" or "process")
        job_template: Job template configuration for the work pool (required for ECS pools)
    """
    try:
        # Try to create the work pool first
        print(f"Creating {pool_type} work pool '{work_pool_name}'...")

        create_params = {
            "name": work_pool_name,
            "type": pool_type,
            "description": f"{pool_type.upper()} work pool for {work_pool_name}",
        }

        # Add job template only for ECS pools
        if pool_type == "ecs" and job_template:
            create_params["base_job_template"] = job_template

        await client.create_work_pool(WorkPoolCreate(**create_params))
        print(f"Work pool '{work_pool_name}' created successfully.")

    except ObjectAlreadyExists:
        # Work pool already exists, update it instead
        print(f"Work pool '{work_pool_name}' already exists. Updating it...")

        update_params = {
            "is_paused": False,
            "description": f"{pool_type.upper()} work pool for {work_pool_name}",
            "concurrency_limit": None,
        }

        # Add job template only for ECS pools
        if pool_type == "ecs" and job_template:
            update_params["base_job_template"] = job_template

        await client.update_work_pool(
            work_pool_name=work_pool_name,
            work_pool=WorkPoolUpdate(**update_params),
        )
        print(f"Work pool '{work_pool_name}' updated successfully.")

    except Exception as e:
        # Handle any other unexpected errors
        print(f"Error managing work pool '{work_pool_name}': {e}")
        raise


def get_ec2_instance_ip(instance_id: str, aws_region: str = "us-east-2") -> str:
    """
    Get the IP address of an EC2 instance from its instance ID.

    Args:
        instance_id (str): The EC2 instance ID (e.g., 'i-09135840c792232fb')
        aws_region (str): AWS region for the EC2 client

    Returns:
        str: The public IP address of the instance, or private IP if public IP is not available

    Raises:
        ClientError: If the instance is not found or AWS API call fails
        RuntimeError: If the instance has no IP addresses assigned
    """
    try:
        # Create EC2 client
        ec2_client = boto3.client("ec2", region_name=aws_region)

        # Describe the instance
        response = ec2_client.describe_instances(InstanceIds=[instance_id])

        if not response["Reservations"]:
            raise ClientError(
                error_response={"Error": {"Code": "InvalidInstanceID.NotFound"}},
                operation_name="DescribeInstances",
            )

        instance = response["Reservations"][0]["Instances"][0]

        # Get public IP first, fallback to private IP
        public_ip = instance.get("PublicIpAddress")
        private_ip = instance.get("PrivateIpAddress")

        if public_ip:
            return public_ip
        elif private_ip:
            return private_ip
        else:
            raise RuntimeError(f"No IP address found for instance {instance_id}")

    except ClientError as e:
        if e.response["Error"]["Code"] == "InvalidInstanceID.NotFound":
            raise ClientError(
                error_response={
                    "Error": {
                        "Code": "InvalidInstanceID.NotFound",
                        "Message": f"Instance {instance_id} not found",
                    }
                },
                operation_name="DescribeInstances",
            )
        raise
    except Exception as e:
        raise RuntimeError(f"Error retrieving IP for instance {instance_id}: {e}")


def load_bootstrap_configuration() -> tuple[dict, dict, str]:
    """
    Load bootstrap configuration and AWS configuration.

    Returns:
        tuple: (bootstrap_config, aws_config, aws_region)
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bootstrap_config_path = os.getenv(
        "BOOTSTRAP_CONFIG_PATH",
        os.path.join(current_dir, "prefect_bootstrap_config.yaml"),
    )
    bootstrap_config = get_config(bootstrap_config_path)
    aws_config = get_aws_config()
    aws_region = aws_config.get("region")

    return bootstrap_config, aws_config, aws_region


async def get_github_token(aws_config: dict, aws_region: str) -> str:
    """
    Retrieve GitHub token from environment variables or AWS Secrets Manager.

    Args:
        aws_config: AWS configuration dictionary
        aws_region: AWS region string

    Returns:
        str: GitHub token
    """
    github_token = os.getenv("GHUB_TOKEN", None)
    if github_token:
        print(f"GitHub token env var used '{github_token}'")
        return github_token

    print(
        "GitHub token not found in environment variables, trying AWS Secrets Manager..."
    )
    secrets_list = get_secrets_from_config(aws_config.get("infrastructure", {}))
    github_token_secret = next(
        (
            secret
            for secret in secrets_list
            if secret["secret_value_env"] == "GHUB_TOKEN"
        ),
        None,
    )

    if github_token_secret:
        github_token = get_secret_value(github_token_secret["secret_name"], aws_region)
        if github_token:
            print("GitHub token retrieved from AWS Secrets Manager")
            return github_token
        else:
            print("Failed to retrieve GitHub token from AWS Secrets Manager")
    else:
        print("GitHub token secret not found in AWS configuration")

    return github_token


def prepare_work_pool_resources(
    pool_name: str, aws_config: dict, aws_region: str
) -> dict:
    """
    Prepare and gather all resources needed for work pool creation.

    Args:
        pool_name: Name of the work pool
        aws_config: AWS configuration dictionary
        aws_region: AWS region string

    Returns:
        dict: Dictionary containing all prepared resources
    """
    relevant_process_resource_ids = get_process_resource_ids(aws_config, pool_name)
    transformed_resources = transform_resource_ids_with_arns(
        relevant_process_resource_ids, aws_config
    )

    cluster = transformed_resources.get("cluster", [{}])[0].get("arn", "")
    task_definition_arn = transformed_resources.get("tasks", [{}])[0].get("arn", "")

    vpc_arn = get_param_values_from_stack_config(
        aws_config.get("infrastructure", {}).get("stacks", {}),
        transformed_resources.get("vpc", [{}])[0].get("id", ""),
    )
    subnets = get_subnets_by_name_keyword(vpc_arn, "private", aws_region=aws_region)

    security_groups = []
    if "security-groups" in transformed_resources:
        security_groups = extract_arns_from_resource(
            transformed_resources.get("security-groups")
        )

    execution_role_arn = transformed_resources.get("iam", [{}])[0].get("arn", "")
    ecr_image_uri = get_image_uri_from_task_arn(task_definition_arn, aws_region)

    return {
        "cluster": cluster,
        "task_definition_arn": task_definition_arn,
        "vpc_arn": vpc_arn,
        "subnets": subnets,
        "security_groups": security_groups,
        "execution_role_arn": execution_role_arn,
        "ecr_image_uri": ecr_image_uri,
    }


async def create_ecs_work_pool(
    client, pool_name: str, aws_config: dict, aws_region: str
) -> None:
    """
    Create an ECS work pool with AWS resources.

    Args:
        client: Prefect client instance
        pool_name: Name of the work pool
        aws_config: AWS configuration dictionary
        aws_region: AWS region string
    """
    resources = prepare_work_pool_resources(pool_name, aws_config, aws_region)

    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, "pool_job_template.json")
    raw_pool_job_template = retrieve_job_template(template_path)

    defaults = {
        "image": resources["ecr_image_uri"],
        "cluster": resources["cluster"],
        "task_definition_arn": resources["task_definition_arn"],
        "task_role_arn": resources["execution_role_arn"],
        "vpc_id": resources["vpc_arn"],
        "subnets": resources["subnets"],
        "security_groups": resources["security_groups"],
    }

    pool_job_template = substitute_job_template_defaults(
        raw_pool_job_template, defaults
    )
    await ensure_work_pool(client, pool_name, "ecs", pool_job_template)
    return resources["ecr_image_uri"]


async def create_process_work_pool(client, pool_name: str) -> str:
    """
    Create a process work pool.

    Args:
        client: Prefect client instance
        pool_name: Name of the work pool

    Returns:
        str: Default image URI for process pools
    """
    await ensure_work_pool(client, pool_name, "process")
    return


async def create_work_pool_by_type(
    client, pool_name: str, pool_type: str, aws_config: dict, aws_region: str
) -> str:
    """
    Create work pool based on the specified type.

    Args:
        client: Prefect client instance
        pool_name: Name of the work pool
        pool_type: Type of the work pool ("ecs" or "process")
        aws_config: AWS configuration dictionary
        aws_region: AWS region string

    Returns:
        str: Image URI to use for deployments
    """
    if pool_type == "ecs":
        return await create_ecs_work_pool(client, pool_name, aws_config, aws_region)
    elif pool_type == "process":
        return await create_process_work_pool(client, pool_name)
    else:
        raise ValueError(f"Unsupported pool type: {pool_type}")


async def create_work_pool(client, pool_name: str, resources: dict) -> None:
    """
    Create work pool with the provided resources.

    Args:
        client: Prefect client instance
        pool_name: Name of the work pool
        resources: Dictionary containing prepared resources
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, "pool_job_template.json")
    raw_pool_job_template = retrieve_job_template(template_path)

    defaults = {
        "image": resources["ecr_image_uri"],
        "cluster": resources["cluster"],
        "task_definition_arn": resources["task_definition_arn"],
        "task_role_arn": resources["execution_role_arn"],
        "vpc_id": resources["vpc_arn"],
        "subnets": resources["subnets"],
        "security_groups": resources["security_groups"],
    }

    pool_job_template = substitute_job_template_defaults(
        raw_pool_job_template, defaults
    )
    await ensure_work_pool(client, pool_name, "ecs", pool_job_template)


async def get_flow_source(
    github_token: str, flow_path: str, import_type: str = "github"
):
    """
    Retrieve and configure the flow source based on import type.

    Args:
        github_token: GitHub authentication token
        flow_path: Path to the flow entrypoint or module path
        import_type: Type of import ("github" or "direct")

    Returns:
        Flow source object or flow function based on import type
    """
    if import_type == "direct":
        # Parse file path and function name
        if ":" in flow_path:
            file_path, flow_name = flow_path.rsplit(":", 1)
        else:
            raise ValueError(
                "Direct import flow-path must include function name after ':'"
            )

        # Convert file path to module path
        module_path = file_path.replace("/", ".").replace(".py", "")

        module = importlib.import_module(module_path)
        flow_fn = getattr(module, flow_name)
        return flow_fn

    # Default to GitHub import
    github_branch = os.getenv("GHUB_BRANCH", "dev")

    # Create and save GitHub credentials block
    github_credentials_block = GitHubCredentials(token=github_token)
    await github_credentials_block.save(name="github-token", overwrite=True)

    # Load the saved credentials block
    saved_credentials = await GitHubCredentials.load("github-token")

    flow_repo = GitRepository(
        url="https://github.com/hentzthename/betting_exchange.git",
        credentials=saved_credentials,
        branch=github_branch,
    )

    return await flow.from_source(source=flow_repo, entrypoint=flow_path)


async def create_deployment(
    client, task: dict, pool_name: str, github_token: str, image_uri: str
) -> None:
    """
    Create a deployment for a specific task.

    Args:
        client: Prefect client instance
        task: Task configuration dictionary
        pool_name: Name of the work pool
        github_token: GitHub authentication token
        image_uri: Image URI for the deployment
    """
    task_name = task.get("name")
    flow_path = task.get("flow-path")
    import_type = task.get("import-type", "github")

    existing_deployments = await client.read_deployments(
        deployment_filter=DeploymentFilter(name={"any_": [task_name]})
    )

    print(f"Creating deployment '{task_name}'...")

    flow_source = await get_flow_source(github_token, flow_path, import_type)

    deployment = await flow_source.deploy(
        name=task_name,
        work_pool_name=pool_name,
        image=image_uri,
        build=False,
        push=False,
        job_variables={
            "env": {"PREFECT_API_URL": f"http://prefect.internal.local:4200/api"},
        },
    )
    print(f"Deployment '{task_name}' registered with ID: {deployment}")


async def main():
    """Main orchestration function for Prefect operational bootstrap."""
    bootstrap_config, aws_config, aws_region = load_bootstrap_configuration()
    github_token = await get_github_token(aws_config, aws_region)

    async with get_client() as client:
        for pool in bootstrap_config.get("pools"):
            pool_name = pool.get("pool-name")
            pool_type = pool.get(
                "type", "ecs"
            )  # Default to ECS for backward compatibility

            # Create work pool based on type
            image_uri = await create_work_pool_by_type(
                client, pool_name, pool_type, aws_config, aws_region
            )

            # Create deployments for each task in the pool
            for task in pool.get("tasks", []):
                await create_deployment(
                    client, task, pool_name, github_token, image_uri
                )


if __name__ == "__main__":
    asyncio.run(main())


def lambda_handler(event, context):
    """AWS Lambda handler function"""
    return asyncio.run(main())
