pools:
  - pool-name: "ecs-pool" # Remember to update the docker-compose with a worker for each new pool
    type: "ecs"
    tasks:
      - name: "prefect-extraction-task"
        import-type: "github"
        flow-path: "src/tasks/extraction/extraction.py:extraction"

  - pool-name: "local-pool"
    type: "process"
    tasks:
      - name: "main-orchestration-deployment"
        import-type: "github"
        flow-path: "src/lambdas/prefect_operational_bootstrap/flows/main_orchestration.py:main_orchestration_flow"
