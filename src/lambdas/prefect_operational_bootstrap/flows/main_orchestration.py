from typing import Any, Dict

from prefect import flow
from prefect.deployments import run_deployment


@flow
async def main_orchestration_flow() -> Dict[str, Any]:
    """
    Main orchestration flow that triggers multiple ECS deployment runs sequentially.

    This flow runs deployments in order while yielding control to avoid CPU blocking.
    """
    print("Starting main orchestration flow...")

    # Run first deployment and wait for completion
    run1 = await _run_deployment_async("extraction/prefect-extraction-task")
    flow_run_1 = await run1.wait()
    print(f"Flow 1 complete with status: {flow_run_1.state}")

    # Run second deployment and wait for completion
    run2 = await _run_deployment_async("extraction/prefect-extraction-task")
    flow_run_2 = await run2.wait()
    print(f"Flow 2 complete with status: {flow_run_2.state}")

    return _build_result(flow_run_1, flow_run_2)


async def _run_deployment_async(deployment_name: str):
    """Run a deployment asynchronously to avoid blocking."""
    print(f"Triggering deployment: {deployment_name}")
    return await run_deployment(name=deployment_name, timeout=0)


def _build_result(flow_run_1, flow_run_2) -> Dict[str, Any]:
    """Build the return result dictionary."""
    return {
        "run1_id": str(flow_run_1.id),
        "run2_id": str(flow_run_2.id),
        "run1_status": str(flow_run_1.state),
        "run2_status": str(flow_run_2.state),
    }
