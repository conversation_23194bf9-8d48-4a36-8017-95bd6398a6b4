# Build configuration for different services
# Default configuration applies to all services unless overridden
default:
  architectures:
    - linux/arm64
  builder: buildx
  dockerfile: Dockerfile
  # Enable inline cache for better performance
  cache_inline: true

# Service-specific overrides
services:
  # <PERSON><PERSON><PERSON><PERSON> needs AMD64 for development environment compatibility
  "devcontainer/devcontainer":
    architectures:
      - linux/amd64
    builder: classic
    dockerfile: Dockerfile

  # All other services use ARM64 by default (inherits from default config)
  # You can add specific overrides here if needed, for example:

  "prefect/operational_bootstrap":
    architectures:
      - linux/amd64
    builder: classic
    dockerfile: Dockerfile

  # "prefect/server":
  #   architectures:
  #     - linux/arm64
  #   builder: buildx
  #   dockerfile: Dockerfile

  # "prefect/worker":
  #   architectures:
  #     - linux/arm64
  #   builder: buildx
  #   dockerfile: Dockerfile

  # "extraction/extraction":
  #   architectures:
  #     - linux/arm64
  #   builder: buildx
  #   dockerfile: Dockerfile
