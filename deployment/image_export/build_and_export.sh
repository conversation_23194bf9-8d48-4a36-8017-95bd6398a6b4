#!/bin/bash
# /betting_exchange/deployment/image_export/build_and_export.sh
# /betting_exchange/deployment/image_export/build_and_export.sh docker/prefect

# Source shared functions
SCRIPT_DIR=$(dirname "$(realpath "$0")")
ROOT_PATH=$(realpath "$SCRIPT_DIR/../..")
source "$ROOT_PATH/shared/bash/functions.sh" || { echo "ERROR: Failed to source shared functions"; exit 1; }

echo "Building and exporting Docker images..."

# Install yq if not available (for YAML parsing)
install_yq() {
    if ! command -v yq &> /dev/null; then
        echo "Installing yq for YAML parsing..."
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            sudo apt-get update && sudo apt-get install -y wget
            sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
            sudo chmod +x /usr/local/bin/yq
            elif command -v yum &> /dev/null; then
            # RHEL/CentOS
            sudo yum install -y wget
            sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
            sudo chmod +x /usr/local/bin/yq
        else
            handle_error "Cannot install yq: unsupported package manager"
        fi
    fi
}

# Parse build configuration for a service
get_service_config() {
    local service_path="$1"
    local config_file="$SCRIPT_DIR/build_config.yaml"

    if [ ! -f "$config_file" ]; then
        handle_error "Build configuration file not found: $config_file"
    fi

    # Get service key (relative path from docker/ directory)
    local service_key=$(echo "$service_path" | sed "s|$BASE_REPO_PATH/||")

    # Get service-specific config or use default
    local architectures=$(yq eval ".services.\"$service_key\".architectures // .default.architectures" "$config_file" 2>/dev/null)
    local builder=$(yq eval ".services.\"$service_key\".builder // .default.builder" "$config_file" 2>/dev/null)
    local dockerfile=$(yq eval ".services.\"$service_key\".dockerfile // .default.dockerfile" "$config_file" 2>/dev/null)
    local cache_inline=$(yq eval ".services.\"$service_key\".cache_inline // .default.cache_inline" "$config_file" 2>/dev/null)

    # Convert YAML array to comma-separated string for architectures
    if [ "$architectures" != "null" ]; then
        architectures=$(echo "$architectures" | yq eval 'join(",")' -)
    fi

    # Default cache_inline to false if not set
    if [ "$cache_inline" = "null" ]; then
        cache_inline="false"
    fi

    echo "$architectures|$builder|$dockerfile|$cache_inline"
}

# Setup multi-platform builder if it doesn't exist
setup_multiplatform_builder() {
    local builder_name="multiplatform-builder"
    
    # Check if builder exists
    if ! docker buildx ls | grep -q "$builder_name"; then
        echo "Creating multi-platform builder..."
        docker buildx create --name "$builder_name" --driver docker-container --use || handle_error "Failed to create multi-platform builder"
        docker buildx inspect --bootstrap || handle_error "Failed to bootstrap builder"
    else
        echo "Using existing multi-platform builder..."
        docker buildx use "$builder_name" || handle_error "Failed to use existing builder"
    fi
}

# Setup builder based on configuration
setup_builder() {
    local builder_type="$1"
    
    if [ "$builder_type" = "classic" ]; then
        echo "Using classic Docker builder..."
        docker buildx use default || handle_error "Failed to switch to classic builder"
    else
        setup_multiplatform_builder
    fi
}

# Build Docker image with specified configuration
build_docker_image() {
    local dockerfile_path="$1"
    local architectures="$2"
    local builder_type="$3"
    local image_uri_versioned="$4"
    local image_uri_latest="$5"
    local build_context="$6"
    local cache_image_uri="$7"
    local cache_inline="$8"

    setup_builder "$builder_type"

    if [ "$builder_type" = "classic" ]; then
        echo "Building with classic Docker builder for single architecture..."
        # Try to pull cache image for layer caching
        echo "Attempting to pull cache image for layer reuse..."
        docker pull "$cache_image_uri" || echo "No cache image found, building from scratch"

        docker build \
        --quiet \
        --cache-from "$cache_image_uri" \
        -t "$image_uri_versioned" \
        -t "$image_uri_latest" \
        -f "$dockerfile_path" \
        "$build_context" || handle_error "Failed to build Docker image with classic builder"

        echo "Pushing images..."
        docker push "$image_uri_versioned" || handle_error "Failed to push versioned image"
        docker push "$image_uri_latest" || handle_error "Failed to push latest image"
    else
        echo "Building with Docker buildx for platform(s): $architectures"
        echo "Using registry cache: $cache_image_uri"

        # Build cache arguments
        CACHE_ARGS=""

        # Add GitHub Actions cache if available
        if [ -d "/tmp/.buildx-cache" ]; then
            CACHE_ARGS="--cache-from type=local,src=/tmp/.buildx-cache --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max"
        fi

        # Add registry cache
        CACHE_ARGS="$CACHE_ARGS --cache-from type=registry,ref=$cache_image_uri --cache-to type=registry,ref=$cache_image_uri,mode=max"

        # Add inline cache if enabled
        if [ "$cache_inline" = "true" ]; then
            CACHE_ARGS="$CACHE_ARGS --cache-to type=inline"
        fi

        docker buildx build \
        --platform "$architectures" \
        --quiet \
        $CACHE_ARGS \
        -t "$image_uri_versioned" \
        -t "$image_uri_latest" \
        -f "$dockerfile_path" \
        "$build_context" \
        --push || handle_error "Failed to build Docker image with buildx"

        # Move cache to preserve it for next run
        if [ -d "/tmp/.buildx-cache-new" ]; then
            rm -rf /tmp/.buildx-cache
            mv /tmp/.buildx-cache-new /tmp/.buildx-cache
        fi
    fi
}

# Determine the base repository path (up two directories, then into "docker")
BASE_REPO_PATH=$(realpath "$SCRIPT_DIR/../../docker") || handle_error "Failed to determine base repository path"

# Determine if a specific repo path was provided as an argument
if [ -z "$1" ]; then
    echo "No argument provided. Processing all subfolders in '$BASE_REPO_PATH'."
    TARGET_FOLDERS=("$BASE_REPO_PATH"/*)  # Get all subdirectories inside 'docker/'
else
    TARGET_FOLDERS=("$1")
    echo "Processing single repository folder: $1"
fi

# Ensure the base directory exists
if [ ! -d "$BASE_REPO_PATH" ]; then
    handle_error "Directory '$BASE_REPO_PATH' does not exist"
fi

# Install yq for YAML parsing
install_yq

# Get AWS account ID and ECR URL
AWS_ACCOUNT_ID=$(get_aws_account_id)
ECR_URL=$(get_ecr_url "$AWS_ACCOUNT_ID")
echo "Using ECR URL: $ECR_URL"

# Authenticate Docker to AWS ECR
login_to_ecr "$ECR_URL"

# Iterate over the specified target folders
for REPO_PATH in "${TARGET_FOLDERS[@]}"; do
    if [ ! -d "$REPO_PATH" ]; then
        echo "Skipping '$REPO_PATH' - Not a valid directory."
        continue
    fi
    
    REPO_NAME=$(basename "$REPO_PATH")
    
    # Check if the repository exists in ECR
    EXISTING_REPO=$(aws ecr describe-repositories --repository-names "$REPO_NAME" --region "$AWS_REGION" 2>/dev/null || true)
    
    if [ -z "$EXISTING_REPO" ]; then
        echo "ECR repository '$REPO_NAME' does not exist. Creating..."
        aws ecr create-repository --repository-name "$REPO_NAME" --region "$AWS_REGION" > /dev/null 2>&1 || handle_error "Failed to create ECR repository '$REPO_NAME'"
        
        # Apply lifecycle policy to the newly created repository
        POLICY_FILE="$SCRIPT_DIR/ecr_lifecycle_policy.json"
        if [ -f "$POLICY_FILE" ]; then
            echo "Applying lifecycle policy to keep only 5 images for repository '$REPO_NAME'..."
            aws ecr put-lifecycle-policy --repository-name "$REPO_NAME" --lifecycle-policy-text file://"$POLICY_FILE" --region "$AWS_REGION" > /dev/null 2>&1 || handle_error "Failed to apply lifecycle policy to repository '$REPO_NAME'"
        else
            echo "Warning: Lifecycle policy file not found at $POLICY_FILE"
        fi
    fi
    
    # Iterate over subfolders (each is a separate image in the repo)
    for SUBFOLDER in "$REPO_PATH"/*; do
        if [ -d "$SUBFOLDER" ]; then
            IMAGE_NAME=$(basename "$SUBFOLDER")  # Subfolder name as image name

            # Get build configuration for this service
            SERVICE_CONFIG=$(get_service_config "$SUBFOLDER")
            IFS='|' read -r ARCHITECTURES BUILDER_TYPE DOCKERFILE_NAME CACHE_INLINE <<< "$SERVICE_CONFIG"

            # Construct dockerfile path
            DOCKERFILE="$SUBFOLDER/$DOCKERFILE_NAME"

            # Check if Dockerfile exists
            if [ ! -f "$DOCKERFILE" ]; then
                echo "Skipping '$SUBFOLDER' - No Dockerfile found at $DOCKERFILE."
                continue
            fi

            echo "Service: $REPO_NAME/$IMAGE_NAME"
            echo "  Architecture(s): $ARCHITECTURES"
            echo "  Builder: $BUILDER_TYPE"
            echo "  Dockerfile: $DOCKERFILE_NAME"
            echo "  Inline Cache: $CACHE_INLINE"

            # Get the latest tag for this subfolder image
            LATEST_TAG=$(aws ecr list-images --repository-name "$REPO_NAME" --region "$AWS_REGION" --query "imageIds[?contains(imageTag, '$IMAGE_NAME')].[imageTag]" --output text | grep -E "$IMAGE_NAME-[0-9]+" | sort -V | tail -n 1 || true)

            # Extract the last version number and increment it
            if [[ -z "$LATEST_TAG" ]]; then
                NEW_TAG="${IMAGE_NAME}-1"
            else
                LAST_NUM=$(echo "$LATEST_TAG" | sed -E "s/${IMAGE_NAME}-//")
                NEW_NUM=$((LAST_NUM + 1))
                NEW_TAG="${IMAGE_NAME}-${NEW_NUM}"
            fi

            # Define both the incremented tag and the "latest" tag
            IMAGE_URI_VERSIONED="$ECR_URL/$REPO_NAME:$NEW_TAG"
            IMAGE_URI_LATEST="$ECR_URL/$REPO_NAME:${IMAGE_NAME}-latest"
            CACHE_IMAGE_URI="$ECR_URL/$REPO_NAME:${IMAGE_NAME}-cache"

            echo "Building Docker image: $IMAGE_URI_VERSIONED"

            # Build using the configured method
            build_docker_image "$DOCKERFILE" "$ARCHITECTURES" "$BUILDER_TYPE" "$IMAGE_URI_VERSIONED" "$IMAGE_URI_LATEST" "$ROOT_PATH" "$CACHE_IMAGE_URI" "$CACHE_INLINE"

            echo "Successfully pushed $IMAGE_URI_VERSIONED and $IMAGE_URI_LATEST to ECR."
        fi
    done
done

echo "All Docker images have been successfully built and pushed to ECR."
