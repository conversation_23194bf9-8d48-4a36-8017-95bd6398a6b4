# Docker Build Configuration System

This directory contains a configuration-based Docker build system that allows you to specify different architectures and builders for different services.

## Overview

The build system now supports:
- **Architecture-specific builds**: ARM64 (default) or AMD64
- **Builder selection**: Docker Buildx (default) or Classic Docker builder
- **Configuration-based**: Centralized configuration in `build_config.yaml`

## Configuration File

The `build_config.yaml` file controls how each service is built:

```yaml
# Default configuration (applies to all services unless overridden)
default:
  architectures:
    - linux/arm64
  builder: buildx
  dockerfile: Dockerfile

# Service-specific overrides
services:
  "devcontainer/devcontainer":
    architectures:
      - linux/amd64
    builder: classic
    dockerfile: Dockerfile
```

## Current Configuration

Based on your requirements:
- **Default**: All services build for ARM64 using Docker Buildx
- **devcontainer/devcontainer**: Builds for AMD64 using classic Docker builder
- **All other services** (prefect/server, prefect/worker, extraction/extraction, etc.): Build for ARM64 using Docker Buildx

## Usage

### Build All Services
```bash
./deployment/image_export/build_and_export.sh
```

### Build Specific Service
```bash
./deployment/image_export/build_and_export.sh docker/prefect/server
```

## Adding New Service Configurations

To configure a new service, add it to the `services` section in `build_config.yaml`:

```yaml
services:
  "your-service/your-image":
    architectures:
      - linux/amd64
      - linux/arm64  # Multi-arch build
    builder: buildx
    dockerfile: Dockerfile.custom
```

## Configuration Options

### Architectures
- `linux/arm64` - ARM 64-bit (default)
- `linux/amd64` - x86 64-bit
- Multiple architectures for multi-arch builds

### Builders
- `buildx` - Docker Buildx (supports multi-platform, default)
- `classic` - Classic Docker builder (single platform, faster for single-arch)

### Dockerfile
- `Dockerfile` - Default Dockerfile name
- Custom names like `Dockerfile.amd64`, `Dockerfile.arm64`, etc.

## How It Works

1. **Configuration Parsing**: The script reads `build_config.yaml` using `yq`
2. **Service Detection**: For each service, it looks up configuration or uses defaults
3. **Builder Setup**: Sets up the appropriate Docker builder (classic or buildx)
4. **Architecture-Specific Build**: Builds for the specified architecture(s)
5. **ECR Push**: Pushes the built images to AWS ECR

## Dependencies

- `yq` - YAML processor (automatically installed by the script)
- Docker with Buildx support
- AWS CLI configured for ECR access

## Migration from Previous System

The previous system built all images for both `linux/amd64` and `linux/arm64` using Docker Buildx. The new system:

1. **Defaults to ARM64 only** (as requested)
2. **Uses classic builder for AMD64-only builds** (faster)
3. **Allows per-service configuration** (flexible)
4. **Maintains backward compatibility** (existing Dockerfiles work unchanged)

## Troubleshooting

### yq Installation Issues
The script automatically installs `yq` if not present. If installation fails, manually install:
```bash
curl -L https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -o /tmp/yq
sudo mv /tmp/yq /usr/local/bin/yq
sudo chmod +x /usr/local/bin/yq
```

### Builder Issues
- **Classic builder fails**: Ensure Docker daemon is running
- **Buildx fails**: Check if buildx plugin is installed: `docker buildx version`

### Configuration Issues
- **Service not found**: Check the service path matches the directory structure under `docker/`
- **Invalid YAML**: Validate the configuration file: `yq eval . build_config.yaml`
