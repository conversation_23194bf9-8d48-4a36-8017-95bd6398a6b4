from aws_cdk import aws_iam as iam
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter
from shared.python.utils.get_aws_config import get_aws_account_id


def _create_assume_role_principal(enable_local_testing: bool, deployment_env: str):
    """
    Helper function to create the appropriate principal for role assumption.
    Adds local testing trust policy for dev environment if enabled.
    """
    principals = [iam.ServicePrincipal("ecs-tasks.amazonaws.com")]

    if enable_local_testing and deployment_env == "dev":
        account_id = get_aws_account_id()
        # Add local developer to trust policy for dev environment
        principals.append(iam.ArnPrincipal(f"arn:aws:iam::{account_id}:user/drew"))

    return iam.CompositePrincipal(*principals)


def _create_task_execution_role(
    scope: Construct,
    role_id: str,
    enable_local_testing: bool = False,
    deployment_env: str = None,
    secrets_arns: list = None,
    bucket_arns: list = None,
):
    """
    Helper function to create an ECS task execution role.
    This role is used by ECS to pull container images, publish logs, and access secrets
    required during container startup.
    """
    principal = _create_assume_role_principal(enable_local_testing, deployment_env)

    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=principal,
        managed_policies=[
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "service-role/AmazonECSTaskExecutionRolePolicy"
            )
        ],
    )

    # Add permissions to access secrets if provided (only for secrets used during task startup)
    if secrets_arns:
        secrets_policy = iam.PolicyStatement(
            actions=["secretsmanager:GetSecretValue"], resources=secrets_arns
        )
        role.add_to_policy(secrets_policy)

    # Add S3 permissions if bucket ARNs are provided
    if bucket_arns:
        # Create bucket ARNs with /* suffix to allow object operations
        bucket_object_arns = [f"{arn}/*" for arn in bucket_arns]
        all_bucket_arns = bucket_arns + bucket_object_arns

        s3_policy = iam.PolicyStatement(
            actions=[
                "s3:GetObject",
                "s3:PutObject",
                "s3:ListBucket",
                "s3:DeleteObject",
            ],
            resources=all_bucket_arns,
        )
        role.add_to_policy(s3_policy)

    return role


def _create_task_role(
    scope: Construct,
    role_id: str,
    enable_local_testing: bool = False,
    deployment_env: str = None,
    bucket_arns: list = None,
):
    """
    Helper function to create an ECS task role.
    This role provides permissions that the actual application code running in the container needs.
    """
    principal = _create_assume_role_principal(enable_local_testing, deployment_env)

    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=principal,
    )

    # Add application-specific permissions
    # Allow the application to read/write to S3
    if bucket_arns:
        # Create bucket ARNs with /* suffix to allow object operations
        bucket_object_arns = [f"{arn}/*" for arn in bucket_arns]
        all_bucket_arns = bucket_arns + bucket_object_arns

        s3_policy = iam.PolicyStatement(
            actions=[
                "s3:GetObject",
                "s3:PutObject",
                "s3:ListBucket",
                "s3:DeleteObject",
            ],
            resources=all_bucket_arns,
        )
        role.add_to_policy(s3_policy)
    else:
        # If no specific buckets are provided, add read-only access as a baseline
        role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name("AmazonS3ReadOnlyAccess")
        )

    # Add permissions for the application to access Secrets Manager
    role.add_to_policy(
        iam.PolicyStatement(
            actions=[
                "secretsmanager:GetSecretValue",
                "secretsmanager:DescribeSecret",
                "secretsmanager:ListSecrets",
            ],
            resources=["*"],  # Can be restricted to specific secrets if needed
        )
    )

    return role


def create_iam_roles_from_config(
    scope: Construct, iam_config, deployment_env=None, secrets=None, buckets=None
):
    created_roles = {}

    # Extract secret ARNs from actual secret objects if available
    secret_arns = []
    if secrets:
        # Get ARNs from the created secret objects
        secret_arns = [secret.secret_arn for secret in secrets.values()]

    # Extract bucket ARNs from actual bucket objects if available
    bucket_arns = []
    if buckets:
        bucket_arns = [bucket.bucket_arn for bucket in buckets.values()]

    created_roles = {}
    for role_info in iam_config:
        role_id = role_info.get("id")
        enable_local_testing = role_info.get("enable-local-testing-trust-policy", False)

        # Determine role type and create appropriate role
        if "task-execution-role" in role_id:
            role = _create_task_execution_role(
                scope,
                role_id,
                enable_local_testing,
                deployment_env,
                secret_arns,
                bucket_arns,
            )
            created_roles["task-execution"] = role
        elif "task-role" in role_id:
            role = _create_task_role(
                scope, role_id, enable_local_testing, deployment_env, bucket_arns
            )
            created_roles["task"] = role
        else:
            # Skip if we don't recognize the role type
            continue

        create_ssm_parameter(
            scope,
            config=role_info,
            value=role.role_arn,
        )

    return created_roles
