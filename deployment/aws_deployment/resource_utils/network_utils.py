from aws_cdk import Duration
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_route53 as route53
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_vpc_from_config(scope: Construct, vpc_config):
    vpc_id = vpc_config.get("id")

    # Create the VPC with DNS settings enabled
    vpc = ec2.Vpc(
        scope,
        vpc_id,
        vpc_name=vpc_id,
        max_azs=2,
        subnet_configuration=[
            ec2.SubnetConfiguration(
                name="public", subnet_type=ec2.SubnetType.PUBLIC, cidr_mask=24
            ),
            ec2.SubnetConfiguration(
                name="private",
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                cidr_mask=24,
            ),
        ],
        nat_gateways=1,  # No NAT gateways as per requirements
        enable_dns_hostnames=True,  # Enable DNS hostnames
        enable_dns_support=True,  # Enable DNS resolution
    )

    create_ssm_parameter(
        scope,
        config=vpc_config,
        value=vpc.vpc_id,
    )

    return vpc


def _create_security_group(scope: Construct, sg_info: str, vpc: ec2.Vpc):
    """Helper function to create a single security group."""
    sg_id = sg_info.get("id")
    security_group = ec2.SecurityGroup(
        scope,
        sg_id,
        security_group_name=sg_id,
        vpc=vpc,
        description=f"Security group for {sg_id}",
    )

    # Apply egress rules from config
    _apply_security_group_rules(security_group, sg_info, vpc)

    create_ssm_parameter(
        scope,
        config=sg_info,
        value=security_group.security_group_id,
    )

    return security_group


def _apply_security_group_rules(
    security_group: ec2.SecurityGroup, sg_info: dict, vpc: ec2.Vpc = None
):
    """Apply security group rules from config"""
    # Process egress rules if defined in config
    egress_rules = sg_info.get("egress-rules", [])

    if not egress_rules:
        # Default rule if no rules specified in config
        security_group.add_egress_rule(
            ec2.Peer.any_ipv4(), ec2.Port.tcp(443), "Allow HTTPS outbound"
        )
    else:
        for rule in egress_rules:
            peer = _get_peer_from_config(
                rule.get("peer", "any-ipv4"), security_group, vpc
            )
            port = _get_port_from_config(rule.get("port", {}))
            description = rule.get("description", "Egress rule")

            security_group.add_egress_rule(peer, port, description)

    # Process ingress rules if defined in config
    ingress_rules = sg_info.get("ingress-rules", [])

    for rule in ingress_rules:
        peer = _get_peer_from_config(rule.get("peer", "any-ipv4"), security_group, vpc)
        port = _get_port_from_config(rule.get("port", {}))
        description = rule.get("description", "Ingress rule")

        security_group.add_ingress_rule(peer, port, description)


def _get_peer_from_config(peer_config, security_group=None, vpc=None):
    """Convert peer config string to ec2.Peer object"""
    if peer_config == "any-ipv4":
        return ec2.Peer.any_ipv4()
    elif peer_config == "any-ipv6":
        return ec2.Peer.any_ipv6()
    elif peer_config == "self" and security_group:
        return ec2.Peer.security_group_id(security_group.security_group_id)
    elif peer_config == "vpc-cidr" and vpc:
        return ec2.Peer.ipv4(vpc.vpc_cidr_block)
    elif peer_config.startswith("cidr:"):
        return ec2.Peer.ipv4(peer_config[5:])
    # Add other peer types as needed
    return ec2.Peer.any_ipv4()  # Default


def _get_port_from_config(port_config):
    """Convert port config to ec2.Port object"""
    port_type = port_config.get("type", "tcp")
    port_number = port_config.get("number", 443)

    if port_type == "tcp":
        return ec2.Port.tcp(port_number)
    elif port_type == "udp":
        return ec2.Port.udp(port_number)
    elif port_type == "all":
        return ec2.Port.all_traffic()
    # Add other port types as needed
    return ec2.Port.tcp(port_number)  # Default


def create_security_groups_from_config(scope: Construct, security_group_config, vpc):
    created_security_groups = {}

    for sg_info in security_group_config:
        sg_id = sg_info.get("id")
        created_security_groups[sg_id] = _create_security_group(scope, sg_info, vpc)

    return created_security_groups


def _create_gateway_endpoint(vpc: ec2.Vpc, endpoint_id: str):
    """Helper function to create a gateway VPC endpoint."""
    service = ec2.GatewayVpcEndpointAwsService.S3
    return vpc.add_gateway_endpoint(
        endpoint_id,
        service=service,
        # Use private subnets instead of public for gateway endpoints
        subnets=[{"subnetType": ec2.SubnetType.PRIVATE_WITH_EGRESS}],
    )


def _create_interface_endpoint(
    vpc: ec2.Vpc,
    endpoint_id: str,
    service: ec2.InterfaceVpcEndpointAwsService,
    security_groups: list,
):
    """Helper function to create an interface VPC endpoint."""
    return vpc.add_interface_endpoint(
        endpoint_id,
        service=service,
        # Explicitly specify we want to use private subnets
        subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
        security_groups=security_groups,
        private_dns_enabled=True,
    )


def _get_interface_service(endpoint_id: str):
    """Helper function to determine the service for an interface VPC endpoint."""
    if "ecr-api" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.ECR
    elif "ecr-dkr" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.ECR_DOCKER
    elif "logs" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS
    # Add more services as needed
    return None


def create_vpc_endpoints_from_config(
    scope: Construct, vpc_endpoint_config, vpc, security_groups
):
    created_endpoints = {}

    # Find the VPC endpoint security group
    vpc_endpoint_sg = None
    for sg_id, sg in security_groups.items():
        if "vpc-endpoint-sg" in sg_id:
            vpc_endpoint_sg = sg
            break

    # If no dedicated VPC endpoint security group found, create a minimal one
    if not vpc_endpoint_sg:
        # This shouldn't happen with our new config, but provides a fallback
        vpc_endpoint_sg = list(security_groups.values())[0] if security_groups else None

    for endpoint_info in vpc_endpoint_config:
        endpoint_id = endpoint_info.get("id")

        # Determine if it's a gateway or interface endpoint
        if "s3" in endpoint_id:
            endpoint = _create_gateway_endpoint(vpc, endpoint_id)
        else:
            service = _get_interface_service(endpoint_id)
            if service and vpc_endpoint_sg:
                endpoint = _create_interface_endpoint(
                    vpc, endpoint_id, service, [vpc_endpoint_sg]
                )
            else:
                # Skip if we don't recognize the service or no security group
                continue

        created_endpoints[endpoint_id] = endpoint

    return created_endpoints


def create_private_hosted_zone_from_config(
    scope: Construct, hosted_zone_config: dict, vpc: ec2.Vpc
) -> route53.PrivateHostedZone:
    """
    Create a private hosted zone from configuration.

    Args:
        scope: The CDK construct scope
        hosted_zone_config: Configuration for the hosted zone
        vpc: The VPC to associate with the hosted zone

    Returns:
        The created private hosted zone
    """
    zone_id = hosted_zone_config.get("id")
    zone_name = hosted_zone_config.get("zone-name")

    # Create the private hosted zone
    hosted_zone = route53.PrivateHostedZone(
        scope,
        zone_id,
        zone_name=zone_name,
        vpc=vpc,
        comment=f"Private hosted zone for {zone_name}",
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=hosted_zone_config,
        value=hosted_zone.hosted_zone_id,
    )

    return hosted_zone


def create_a_record_from_ec2_config(
    scope: Construct,
    a_record_config: dict,
    hosted_zone: route53.PrivateHostedZone,
    ec2_instance,
) -> route53.ARecord:
    """
    Create a single A record from EC2 configuration.

    Args:
        scope: The CDK construct scope
        a_record_config: A record configuration (nested under EC2 config)
        hosted_zone: The hosted zone to create the record in
        ec2_instance: The EC2 instance to point to

    Returns:
        The created A record
    """
    record_id = a_record_config.get("id")
    record_name = a_record_config.get("record-name")

    # Create the A record
    a_record = route53.ARecord(
        scope,
        record_id,
        zone=hosted_zone,
        record_name=record_name,
        target=route53.RecordTarget.from_ip_addresses(ec2_instance.instance_private_ip),
        ttl=Duration.minutes(5),  # 5 minutes TTL
        comment=f"A record for {record_name} pointing to EC2 instance",
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=a_record_config,
        value=f"{record_name}.{hosted_zone.zone_name}",
    )

    return a_record


def create_a_records_from_config(
    scope: Construct,
    a_records_config: list,
    hosted_zone: route53.PrivateHostedZone,
    ec2_instances: dict,
) -> dict:
    """
    Create A records from configuration.

    Args:
        scope: The CDK construct scope
        a_records_config: List of A record configurations
        hosted_zone: The hosted zone to create records in
        ec2_instances: Dictionary of EC2 instances by their config ID

    Returns:
        Dictionary of created A records
    """
    created_records = {}

    for record_config in a_records_config:
        record_id = record_config.get("id")
        record_name = record_config.get("record-name")
        ec2_instance_id = record_config.get("ec2-instance-id")

        # Find the corresponding EC2 instance
        if ec2_instance_id in ec2_instances:
            ec2_instance = ec2_instances[ec2_instance_id]

            # Create the A record
            a_record = route53.ARecord(
                scope,
                record_id,
                zone=hosted_zone,
                record_name=record_name,
                target=route53.RecordTarget.from_ip_addresses(
                    ec2_instance.instance_private_ip
                ),
                ttl=Duration.minutes(5),  # 5 minutes TTL
                comment=f"A record for {record_name} pointing to {ec2_instance_id}",
            )

            created_records[record_id] = a_record

            # Create SSM parameter if specified
            create_ssm_parameter(
                scope,
                config=record_config,
                value=f"{record_name}.{hosted_zone.zone_name}",
            )

    return created_records
