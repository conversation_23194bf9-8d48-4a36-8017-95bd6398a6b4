#!/usr/bin/env python3
# Use the start_orchestrator venv, simple requirements.txt
"""
Scrip<PERSON> to invoke the prefect operational bootstrap lambda function.
This script uses shared functions to get the lambda ARN from the config
and consumer process association.
"""

import sys

import boto3

from shared.python.utils.get_aws_config import get_aws_config
from shared.python.utils.process_resources import (
    get_param_value,
    get_process_resource_ids,
)


def get_lambda_arn_from_config(aws_config: dict, process_name: str) -> str:
    """
    Get the Lambda function ARN using the consumer process name.

    Args:
        aws_config: The AWS configuration dictionary
        process_name: The consumer process name (e.g., 'start_bootstrap_lambda')

    Returns:
        The Lambda function ARN from SSM Parameter Store

    Raises:
        SystemExit: If lambda not found or ARN cannot be retrieved
    """
    try:
        # Get all resources associated with the process
        process_resources = get_process_resource_ids(aws_config, process_name)

        # Find lambda resources
        lambda_resources = process_resources.get("lambda", [])

        if not lambda_resources:
            print(f"No lambda resources found for consumer process: {process_name}")
            sys.exit(1)

        if len(lambda_resources) > 1:
            print(
                f"Multiple lambda resources found for process {process_name}: {lambda_resources}"
            )
            print("Using the first one...")

        lambda_id = lambda_resources[0]
        print(f"Found lambda resource ID: {lambda_id}")

        # Get the lambda ARN from SSM Parameter Store
        ssm_client = boto3.client("ssm")
        lambda_arn = get_param_value(ssm_client, lambda_id)

        print(f"Retrieved lambda ARN: {lambda_arn}")
        return lambda_arn

    except Exception as e:
        print(f"Error getting lambda ARN from config: {e}")
        sys.exit(1)


def invoke_lambda_function(lambda_arn: str, region: str) -> dict:
    """
    Invoke a Lambda function.

    Args:
        lambda_arn: The Lambda function ARN
        region: AWS region

    Returns:
        Response from the Lambda invocation

    Raises:
        SystemExit: If invocation fails
    """
    try:
        lambda_client = boto3.client("lambda", region_name=region)

        print(f"Invoking lambda function synchronously: {lambda_arn}")

        # Invoke the function synchronously (RequestResponse type)
        response = lambda_client.invoke(
            FunctionName=lambda_arn,
            InvocationType="RequestResponse",
        )

        # Process response
        status_code = response.get("StatusCode", 0)

        if status_code == 200:  # 200 is success for sync invocation
            print("✅ Lambda function invoked successfully!")

            # Check for function errors
            if "FunctionError" in response:
                print(f"❌ Lambda function error: {response['FunctionError']}")

                # Try to get error details from payload
                if "Payload" in response:
                    import json

                    payload_content = response["Payload"].read()
                    if payload_content:
                        try:
                            error_data = json.loads(payload_content)
                            print(f"Error details: {json.dumps(error_data, indent=2)}")
                        except json.JSONDecodeError:
                            print(
                                f"Error payload (raw): {payload_content.decode('utf-8')}"
                            )

                sys.exit(1)
            else:
                print("✅ Lambda function executed successfully without errors!")

                # Optionally show the response payload
                if "Payload" in response:
                    import json

                    payload_content = response["Payload"].read()
                    if payload_content:
                        try:
                            response_data = json.loads(payload_content)
                            print(f"Response: {json.dumps(response_data, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"Response (raw): {payload_content.decode('utf-8')}")
        else:
            print(f"⚠️  Lambda function returned status code: {status_code}")
            sys.exit(1)

        return response

    except Exception as e:
        print(f"❌ Error invoking lambda function: {e}")
        sys.exit(1)


def get_region_from_config(aws_config: dict) -> str:
    """
    Get the AWS region from the configuration.

    Args:
        aws_config: The AWS configuration dictionary

    Returns:
        The AWS region string

    Raises:
        SystemExit: If region cannot be retrieved
    """
    try:
        region = aws_config.get("region")

        if not region:
            print("❌ Region not found in AWS config")
            sys.exit(1)

        print(f"Using region from config: {region}")
        return region

    except Exception as e:
        print(f"❌ Error getting region from config: {e}")
        sys.exit(1)


def main():
    """
    Main function to orchestrate the lambda invocation.
    """
    print("🚀 Starting lambda invocation process...")

    # Get AWS configuration once
    try:
        aws_config = get_aws_config()
    except Exception as e:
        print(f"❌ Error loading AWS config: {e}")
        sys.exit(1)

    # Get region from config
    region = get_region_from_config(aws_config)

    # Get lambda ARN from config using consumer process
    consumer_process = "start_bootstrap_lambda"
    lambda_arn = get_lambda_arn_from_config(aws_config, consumer_process)

    # Invoke the lambda function
    _ = invoke_lambda_function(lambda_arn=lambda_arn, region=region)

    print("✅ Lambda invocation completed successfully!")
    print("Lambda was invoked synchronously and completed execution.")


if __name__ == "__main__":
    main()
