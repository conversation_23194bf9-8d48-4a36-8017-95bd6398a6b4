from aws_cdk import Stack
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_route53 as route53
from constructs import Construct

from deployment.aws_deployment.resource_utils.ec2_utils import (
    create_ec2_instance_from_config,
    create_ec2_role_from_config,
    create_ec2_security_group_from_config,
)
from deployment.aws_deployment.resource_utils.extraction_utils import (
    create_secrets_from_config,
)
from deployment.aws_deployment.resource_utils.lambda_utils import (
    create_lambda_from_config,
    create_lambda_role_from_config,
)
from deployment.aws_deployment.resource_utils.network_utils import (
    create_a_record_from_ec2_config,
)


class OrchestratorStack(Stack):
    def __init__(
        self,
        scope: Construct,
        aws_config: dict,
        deployment_env,
        vpc: ec2.Vpc = None,
        bastion_sg_id: str = None,  # Bastion security group ID from CFN output
        private_hosted_zone: route53.PrivateHostedZone = None,
        region: str = None,
        **kwargs,
    ) -> None:
        """
        Initialize a new Orchestrator Stack.

        This stack is responsible for creating AWS resources for Orchestrator,
        including secrets and EC2 instances.

        Args:
            scope: The CDK construct scope
            aws_config: Configuration for the Orchestrator stack
            vpc: VPC from the network stack (optional)
            bastion_sg_id: Bastion security group ID from CFN output (optional)
            private_hosted_zone: Private hosted zone from the network stack (optional)
            **kwargs: Additional arguments to pass to the Stack constructor
        """
        # Initialize the stack with the ID from config
        stack_id = aws_config.get("id")
        super().__init__(scope, stack_id, **kwargs)

        # Create secrets from config
        self.secrets = create_secrets_from_config(
            self,
            secrets_config=aws_config.get("secrets", []),
        )

        # Get EC2 configuration
        ec2_config = aws_config.get("ec2")

        # Create EC2 instance if configuration is provided and VPC is available
        if ec2_config and vpc:
            # Create security group for EC2 instance
            sg_config = ec2_config.get("security-group")
            if sg_config:
                self.ec2_security_group = create_ec2_security_group_from_config(
                    self,
                    sg_config=sg_config,
                    vpc=vpc,
                )

            # Create IAM role for EC2 instance
            role_config = ec2_config.get("iam-role")
            if role_config:
                # Extract secret ARNs for role permissions
                secret_arns = [secret.secret_arn for secret in self.secrets.values()]

                self.ec2_role = create_ec2_role_from_config(
                    self,
                    role_config=role_config,
                    secrets_arns=secret_arns,
                )

            # Get bastion security group by ID if provided
            bastion_sg = None
            if bastion_sg_id:
                bastion_sg = ec2.SecurityGroup.from_security_group_id(
                    self, "BastionSecurityGroup", bastion_sg_id
                )

            # Add ingress rule for SSH from the bastion host if it exists
            if bastion_sg:
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.security_group_id(bastion_sg.security_group_id),
                    ec2.Port.tcp(22),
                    "Allow SSH access from bastion host",
                )

            # Add ingress rules for orchestrator services from private subnet
            # Private subnets typically use ********/24 and ********/24 in a /16 VPC
            # We'll allow from the entire private subnet range for simplicity
            for private_subnet in vpc.private_subnets:
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.ipv4(private_subnet.ipv4_cidr_block),
                    ec2.Port.tcp(4200),
                    f"Allow Prefect UI access from private subnet {private_subnet.availability_zone}",
                )
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.ipv4(private_subnet.ipv4_cidr_block),
                    ec2.Port.tcp(443),
                    f"Allow HTTPS access from private subnet {private_subnet.availability_zone}",
                )
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.ipv4(private_subnet.ipv4_cidr_block),
                    ec2.Port.tcp(80),
                    f"Allow HTTP access from private subnet {private_subnet.availability_zone}",
                )

            # Add ingress rules for orchestrator services from public subnet
            for public_subnet in vpc.public_subnets:
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.ipv4(public_subnet.ipv4_cidr_block),
                    ec2.Port.tcp(4200),
                    f"Allow Prefect UI access from public subnet {public_subnet.availability_zone}",
                )

            # Create EC2 instance
            self.ec2_instance = create_ec2_instance_from_config(
                self,
                ec2_config=ec2_config,
                vpc=vpc,
                security_groups=[self.ec2_security_group],
                role=self.ec2_role,
                key_name=f"{deployment_env}-bastion-key-pair",
                secrets_config=aws_config.get("secrets", []),
            )

        # Get Lambda configuration
        lambda_config = aws_config.get("lambda")

        # Create Lambda function if configuration is provided
        if lambda_config and vpc:
            # Create security group for Lambda function
            self.lambda_security_group = ec2.SecurityGroup(
                self,
                f"{lambda_config.get('id')}-sg",
                security_group_name=f"{lambda_config.get('id')}-sg",
                vpc=vpc,
                description="Security group for Lambda function",
                allow_all_outbound=True,
            )

            # Create IAM role for Lambda function
            lambda_role_config = lambda_config.get("iam-role")
            if lambda_role_config:
                self.lambda_role = create_lambda_role_from_config(
                    self,
                    role_config=lambda_role_config,
                )

                # Create the Lambda function with VPC configuration
                self.lambda_function = create_lambda_from_config(
                    self,
                    lambda_config=lambda_config,
                    role=self.lambda_role,
                    deployment_env=deployment_env,
                    region=region,
                    vpc=vpc,
                    security_group=self.lambda_security_group,
                )

        # Create A record for the orchestrator EC2 instance if hosted zone is provided
        if private_hosted_zone and hasattr(self, "ec2_instance"):
            # Get A record configuration nested under EC2 config
            ec2_config = aws_config.get("ec2", {})
            a_record_config = ec2_config.get("a-record")
            if a_record_config:
                self.a_record = create_a_record_from_ec2_config(
                    self,
                    a_record_config=a_record_config,
                    hosted_zone=private_hosted_zone,
                    ec2_instance=self.ec2_instance,
                )
