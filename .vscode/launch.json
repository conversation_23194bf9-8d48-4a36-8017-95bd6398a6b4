{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2"
            }
        },
        {
            "name": "CDK App Testing",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/app.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2"
            }
        },
        {
            "name": "CDK App Synth",
            "type": "node",
            "request": "launch",
            "program": "/workspace/node_modules/aws-cdk/bin/cdk",
            "args": [
                "synth",
                "--app",
                "python ${workspaceFolder}/deployment/aws_deployment/app.py"
            ],
            "cwd": "${workspaceFolder}",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2"
            },
            "console": "integratedTerminal",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "name": "Start Prefect",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/scripts/copy_and_start_prefect.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
            }
        },
        {
            "name": "Copy and Start Orchestrator",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/scripts/start_orchestrator/start_orchestrator.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
            }
        },
        {
            "name": "Invoke Prefect Bootstrap Lambda",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/scripts/start_bootstrap_lambda/invoke_bootstrap_lambda.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
            }
        },
        {
            "name": "Update Secrets",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/deployment_helpers/update_secrets.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
                "API_KEY_BALLDONTLIE": "abc"
            }
        },
        {
            "name": "Extraction",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/tasks/extraction/extraction.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
                "ENDPOINTS": "teams",
                "PREFECT_API_URL": "http://host.docker.internal:4200/api"
            }
        },
        {
            "name": "Prefect Bootstrap",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/lambdas/prefect_operational_bootstrap/prefect_operational_bootstrap.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "BOOTSTRAP_CONFIG_PATH": "${workspaceFolder}/src/lambdas/prefect_operational_bootstrap/prefect_bootstrap_config.yaml",
                "AWS_REGION": "us-east-2",
                // "PREFECT_API_URL": "http://host.docker.internal:4200/api",
                "PREFECT_API_URL": "http://localhost:4201/api",
                "GHUB_BRANCH": "dev"
            }
        }
    ]
}